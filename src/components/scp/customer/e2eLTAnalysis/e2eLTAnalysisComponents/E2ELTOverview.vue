<template>
  <div class="left-sidebar" id="e2eOverview">
    <div class="widget">
      <el-row class="search-box">
        <el-col :span="5">
          <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base-url="/customer/e2e_lt_analysis/query_filters"
                      :filter-base="['MD3_IDS_CLO_SO_DATA_AGG_V']"
                      :after-apply="search" :after-cascader-loaded="afterFilterLoaded"/>
        </el-col>
        <el-col :span="5">
          <el-tooltip effect="light" placement="bottom">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="monthrange"
                unlink-panels
                range-separator="~"
                format="YYYYMM"
                value-format="YYYYMM"
                start-placeholder="Start date"
                end-placeholder="End date"
                :clearable="false">
            </el-date-picker>
          </el-tooltip>
        </el-col>
        <el-col :span="3">
          <el-select v-model="pageCtl.conditions.resultType" size="small">
            <el-option
                v-for="item in ['Number of Records', 'Quantity', 'Net Value', 'Lines']"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="pageCtl.conditions.calendarType" size="small">
            <el-option
                v-for="item in ['Order Creation Date', 'Order Clean Date']"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="pageCtl.conditions.calendarCalcType" size="small">
            <el-option
                v-for="item in ['Working Days', 'Calendar Days']"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="1">
          <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                      :data-exclude="['dateRange']"/>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10" v-loading="pageCtl.loading.report1">
          <div class="subscript-container subscript-container-left" style="height: 400px">
            <scp-subscript id="LTR1" ref="report1SubRef"/>
            <div class="front">
              <chart ref="report1Ref" v-contextmenu:report1ContextmenuRef :height="400" :option="_report1Opt"/>
            </div>
            <div class="back"><!-- 高度已调整 -->
              <div class="box">
                <div class="box-title">Treemap Level Settings</div>
                <el-row>
                  <el-col :span="6">Level1</el-col>
                  <el-col :span="18">
                    <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level1" size="small"
                               placeholder="Select..." filterable>
                      <el-option
                          v-for="item in _pivotColumns"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">Level2</el-col>
                  <el-col :span="18">
                    <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level2" size="small"
                               placeholder="Select..." filterable>
                      <el-option
                          v-for="item in _pivotColumns"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">Level3</el-col>
                  <el-col :span="18">
                    <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level3" size="small"
                               placeholder="Select..." filterable>
                      <el-option
                          v-for="item in _pivotColumns"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">Level4</el-col>
                  <el-col :span="18">
                    <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level4" size="small"
                               placeholder="Select..." clearable
                               filterable>
                      <el-option
                          v-for="item in _pivotColumns"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">Level5</el-col>
                  <el-col :span="18">
                    <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level5" size="small"
                               placeholder="Select..." clearable
                               filterable>
                      <el-option
                          v-for="item in _pivotColumns"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                  <div class="box-title">&nbsp;</div>
                  <el-col :span="6">Display level</el-col>
                  <el-col :span="18">
                    <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.leafDepth" size="small"
                               placeholder="Select...">
                      <el-option
                          v-for="item in [1, 2]"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
              </div>
              <div class="box">
                <div class="box-title">Tooltips</div>
                <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                   style="height:calc(100% - 70px);overflow: auto">
                  <el-checkbox
                      v-for="item in pageCtl.report1TooltipsOpts"
                      :key="item"
                      :value="item"
                      :label="item"
                  ></el-checkbox>
                </el-checkbox-group>
                <div class="box-footer">
                  <el-button
                      @click="report1SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report1SubRef.toggleView();searchReport1()">
                    Search
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="14" v-loading="pageCtl.loading.report2">
          <div class="subscript-container subscript-container-right" style="height: 400px">
            <scp-subscript id="LTR2" ref="report2SubRef"/>
            <div class="front">
              <chart ref="report2Ref" :height="400" :option="_report2Opt"/>
            </div>
            <div class="back">
              <div class="box">
                <div class="box-title">E2E LT Overview Settings</div>
                <el-row>
                  <el-col :span="6">Map Type</el-col>
                  <el-col :span="18">
                    <el-select
                        v-model="pageCtl.conditions.report2MapType" size="small" filterable>
                      <el-option
                          v-for="item in ['GSC_REGION', 'COUNTRY_NAME', 'GSC_SUB_REGION']"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="6">Map Result Type</el-col>
                  <el-col :span="18">
                    <el-select
                        v-model="pageCtl.conditions.report2ResultType" size="small" filterable>
                      <el-option
                          v-for="item in pageCtl.report1TooltipsOpts"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="6">Map Visual Min</el-col>
                  <el-col :span="18">
                    <el-input v-model="pageCtl.conditions.report2VisualStart" size="small"/>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="6">Map Visual Max</el-col>
                  <el-col :span="18">
                    <el-input v-model="pageCtl.conditions.report2VisualEnd" size="small"/>
                  </el-col>
                </el-row>
                <div class="box-footer">
                  <el-button
                      @click="report2SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report2SubRef.toggleView();searchReport2()">Search
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="subscript-container">
        <el-row class="search-box">
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.report3Category" placeholder="Category" filterable clearable multiple
                       collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.report3SelectedColumns" size="small" multiple collapse-tags>
              <el-option
                  v-for="item in pageCtl.report3SelectedColumnsOptions"
                  :key="item.data"
                  :label="item.label"
                  :value="item.data">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="search3Ref" :click-native="searchReport3" :data="pageCtl.conditions" :expand="true"/>
          </el-col>
        </el-row>
        <scp-subscript id="LTR3"/>
        <scp-table
            ref="report3TableRef"
            :columns="pageCtl.report3Columns"
            :last-row-bold="true"
            :params="pageCtl.conditions"
            :after-select="afterReport3Select"
            :lazy="true"
            :pagging="false"
            :max-height="400"
            :fixedColumnsLeft="1"
            :context-menu-items="pageCtl.report3ContextItems"
            :showTotalIgnoreCols="pageCtl.conditions.report3Category"
            url="/customer/e2e_lt_analysis/overview/query_report3"
            download-url="/customer/e2e_lt_analysis/overview/download_report3"
            :editable="false"/>
      </div>
    </div>

    <!--  report1 contextmenu  -->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, onActivated, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $viewDetails: any = inject('$viewDetails')
const $join: any = inject('$join')
const $axios: any = inject('$axios')
const $dateFormatter: any = inject('$dateFormatter')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $toolbox: any = inject('$echarts.toolbox')
const $tooltip: any = inject('$echarts.tooltip')
const $grid: any = inject('$echarts.grid')
const searchRef = ref()
const report1SubRef = ref()
const report1Ref = ref()
const report2Ref = ref()
const report2SubRef = ref()
const report3TableRef = ref()

const viewReport3Details = () => {
  $viewDetails({
    url: '/customer/e2e_lt_analysis/overview/query_report3_details',
    durl: '/customer/e2e_lt_analysis/overview/download_report3_details',
    params: pageCtl.conditions,
    title: 'View E2E LT Details ' + (pageCtl.conditions.report3SelectedValue.length ? ('[' + pageCtl.conditions.report3SelectedValue.join(', ') + ']') : '')
  })
}

const pageCtl = reactive({
  loading: {
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: [{
        joiner: 'AND',
        fields: [
          'GSC_REGION'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'GLOBAL ETO'
      }, {
        joiner: 'AND',
        fields: [
          'LONG_CRD_FLAG_CHAR'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: '1'
      }, {
        joiner: 'AND',
        fields: [
          'OG_IG_FLAG'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'IN',
        value: [],
        text: 'OG'
      }, {
        joiner: 'AND',
        fields: [
          'FIRST_DELAY_FAMILY'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'Credit Control'
      }, {
        joiner: 'AND',
        fields: [
          'LAST_DELAY_FAMILY'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'Credit Control'
      }
      ]
    },
    resultType: 'Net Value',
    level1: 'GSC_SUB_REGION',
    level2: 'ACTUAL_LT_RANGE',
    level3: 'COUNTRY_NAME',
    level4: 'COUNTRY_NAME',
    level5: 'COUNTRY_NAME',
    leafDepth: 2,
    selectedTreePath: '',
    selectedLegend: {},
    report1Tooltips: [
      'ACTUAL_LT',
      'CONTRACTUAL_LT',
      'CUSTOMER_REQUESTED_LT',
      'FIRST_CONFIRMED_LT',
      'LOGISTICS_OFFER_LT'
    ],
    report2ResultType: 'ACTUAL_LT',
    report2MapType: 'GSC_SUB_REGION',
    report2MapExclude: 'Exclude GLOBAL ETO',
    dateRange: [] as any,
    report2DetailsType: '',
    report3SelectedValue: [],
    report3Category: ['GSC_SUB_REGION'],
    report3TotalColumns: [] as any,
    report2VisualStart: '',
    report2VisualEnd: '',
    calendarType: 'Order Creation Date',
    calendarCalcType: 'Working Days'
  },
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  report1Data: [],
  report2Data: [],
  report1TooltipsOpts: [
    'ACTUAL_LT',
    'CONTRACTUAL_LT',
    'CUSTOMER_REQUESTED_LT',
    'FIRST_CONFIRMED_LT',
    'LOGISTICS_OFFER_LT',
    'LINES',
    'NET_VALUE',
    'NUM_OF_RECORDS',
    'QUANTITY'
  ],
  filterOpts: [],
  report3ContextItems: {
    view_details: {
      name: 'View Details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3Columns: [],
  report3SelectedColumns: [
    'Actual LT',
    'Customer Requested LT',
    'Contractual LT',
    'First Confirmed LT',
    'Logistics Offer LT'
  ],
  report3SelectedColumnsOptions: [
    { label: 'ACTUAL LT', data: 'Actual LT' },
    { label: 'CUSTOMER_REQUESTED_LT', data: 'Customer Requested LT' },
    { label: 'CONTRACTUAL_LT', data: 'Contractual LT' },
    { label: 'FIRST_CONFIRMED_LT', data: 'First Confirmed LT' },
    { label: 'LOGISTICS_OFFER_LT', data: 'Logistics Offer LT' }
  ]
})
onActivated(() => {
  if (report1Ref.value || report2Ref.value) {
    setTimeout(() => {
      report1Ref.value?.resize()
      report2Ref.value?.resize()
    }, 0)
  }
})

onMounted(async () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(now.getFullYear(), 0, 1)
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyyMM'),
    $dateFormatter(end, 'yyyyMM')
  ]
  report2Ref.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.report2DetailsType = params.name

    $viewDetails({
      url: '/customer/e2e_lt_analysis/overview/query_report2_details',
      durl: '/customer/e2e_lt_analysis/overview/download_report2_details',
      params: pageCtl.conditions,
      title: 'View Details [' + pageCtl.conditions.report2DetailsType + ']'
    })
  })
})

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/e2e_lt_analysis/overview/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    calculateValues(body)
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/e2e_lt_analysis/overview/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  if (pageCtl.conditions.report3Category === null || pageCtl.conditions.report3Category.length === 0) {
    pageCtl.conditions.report3Category = ['GSC_SUB_REGION']
  }
  parseTableColumn()
  report3TableRef.value.search()
}

const afterReport3Select = (row) => {
  pageCtl.conditions.report3TotalColumns = _report3SelectedCategory.value
  if (row[pageCtl.conditions.report3TotalColumns[0]] === 'Total') {
    pageCtl.conditions.report3SelectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < pageCtl.conditions.report3TotalColumns.length; i++) {
      selectedValue.push(row[pageCtl.conditions.report3TotalColumns[i]])
    }
    pageCtl.conditions.report3SelectedValue = selectedValue
  }
}

const parseTableColumn = () => {
  const columns = [] as any
  for (let i = 0; i < _report3SelectedCategory.value.length; i++) {
    columns.push({
      data: _report3SelectedCategory.value[i],
      title: camelCaseStartPlaceholder(_report3SelectedCategory.value[i])
    })
  }
  columns.push(
    { data: 'MTS_RATIO', title: 'MTS Ratio' },
    { data: 'NUM_OF_RECORDS', title: 'Number of Records', type: 'numeric' },
    { data: 'VALUE', title: 'Net Value', type: 'numeric' }
  )
  for (let i = 0; i < pageCtl.report3SelectedColumns.length; i++) {
    console.log('pageCtl.report3SelectedColumns[i]')
    console.log(pageCtl.report3SelectedColumns[i])
    columns.push({
      data: pageCtl.report3SelectedColumns[i],
      title: pageCtl.report3SelectedColumns[i],
      type: 'numeric'
    })
  }
  pageCtl.report3Columns = columns
}

const afterFilterLoaded = (opts) => {
  pageCtl.filterOpts = opts
  searchRef.value.loadAndClick()
}

const sumValues = (node) => {
  // 如果有子节点，则递归计算子节点的 value 之和
  if (node.children && node.children.length > 0) {
    let sum = 0
    node.children.forEach(child => {
      sum += sumValues(child)
    })
    node.value = sum // 将子节点的和赋值给当前节点
    node.children.sort((a, b) => b.value - a.value)
  }
  return node.value || 0
}

const calculateValues = (tree) => {
  tree.forEach(node => sumValues(node))
  tree.sort((a, b) => b.value - a.value)
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
}

const _pivotColumns = computed(() => {
  const filterTemp = $getPivotColumnByFilter(pageCtl.filterOpts)
  return filterTemp.concat([]).sort()
})

const _report1Opt = computed(() => {
  const rootName = 'E2E LT'
  return {
    title: {
      text: 'End to End LT by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: $tooltip({
      callback: (e) => {
        pageCtl.selectedCurrentLevel = e.selectedCurrentLevel
        pageCtl.selectedParentLevel = e.selectedParentLevel
      }
    }, pageCtl.report1Data),
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report2Opt = computed(() => {
  // const sortedData = [...pageCtl.report2Data].sort((a, b) => b.value - a.value)
  // const topFiftyData = sortedData.slice(0, 10)

  const countryLabel = ['China', 'Canada', 'Algeria', 'United States', 'France', 'Russian Federation', 'India', 'Brazil', 'Australia', 'Kazakhstan']

  let nameMap = {}
  if (pageCtl.conditions.report2MapType === 'GSC_SUB_REGION') {
    nameMap = {
      Mexico: 'NAM',
      Philippines: 'EAJ',
      'United Arab Emirates': 'MEA',
      Brazil: 'SAM',
      Greece: 'SEE',
      Kuwait: 'MEA',
      Ecuador: 'SAM',
      'Russian Federation': 'EUROPE',
      Netherlands: 'BeNe',
      France: 'France',
      'Korea (South)': 'EAJ',
      Hungary: 'SEE',
      Sweden: 'Nordics',
      Germany: 'DACH',
      'Sri Lanka': 'INDIA',
      Australia: 'PACIFIC',
      Israel: 'Israel',
      Poland: 'MEE',
      Argentina: 'SAM',
      Chile: 'SAM',
      Belgium: 'BeNe',
      Ukraine: 'EUROPE',
      'South Africa': 'MEA',
      Kenya: 'MEA',
      Lithuania: 'EUROPE',
      Singapore: 'EAJ',
      'Costa Rica': 'NAM',
      Peru: 'SAM',
      Canada: 'NAM',
      Lebanon: 'MEA',
      Qatar: 'MEA',
      Oman: 'MEA',
      Colombia: 'SAM',
      Algeria: 'MEA',
      Estonia: 'Nordics',
      'Croatia (Hrvatska)': 'EUROPE',
      India: 'INDIA',
      Denmark: 'Nordics',
      Malaysia: 'EAJ',
      'Czech Republic': 'MEE',
      'Saudi Arabia': 'MEA',
      Ireland: 'UK&I',
      Spain: 'Iberia',
      Japan: 'EAJ',
      Turkey: 'MEA',
      'Slovak Republic': 'EUROPE',
      Slovenia: 'SEE',
      China: 'CHINA',
      'United Kingdom': 'UK&I',
      Thailand: 'EAJ',
      Indonesia: 'EAJ',
      Austria: 'DACH',
      Morocco: 'MEA',
      'Hong Kong': 'CHINA',
      Latvia: 'Nordics',
      Pakistan: 'MEA',
      Kazakhstan: 'EUROPE',
      Romania: 'SEE',
      Italy: 'Italy',
      'United States': 'NAM',
      'New Zealand': 'PACIFIC',
      Switzerland: 'DACH',
      Taiwan: 'EAJ',
      'Viet Nam': 'EAJ',
      Egypt: 'MEA',
      Nigeria: 'MEA',
      Bulgaria: 'SEE',
      Serbia: 'SEE',
      Finland: 'Nordics',
      Norway: 'Nordics',
      Portugal: 'Iberia'
    }
  } else if (pageCtl.conditions.report2MapType === 'GSC_REGION') {
    nameMap = {
      Mexico: 'NAM',
      'United Kingdom': 'EUROPE',
      Brazil: 'SAM',
      Belgium: 'EUROPE',
      Latvia: 'EUROPE',
      Ecuador: 'SAM',
      Qatar: 'IMEA',
      Slovenia: 'EUROPE',
      'Russian Federation': 'EUROPE',
      Netherlands: 'EUROPE',
      'New Zealand': 'EAJP',
      Singapore: 'EAJP',
      Austria: 'EUROPE',
      Switzerland: 'EUROPE',
      Ireland: 'EUROPE',
      Bulgaria: 'EUROPE',
      Norway: 'EUROPE',
      Romania: 'EUROPE',
      Thailand: 'EAJP',
      Philippines: 'EAJP',
      Chile: 'SAM',
      Ukraine: 'EUROPE',
      Argentina: 'SAM',
      Turkey: 'IMEA',
      Kuwait: 'IMEA',
      Lithuania: 'EUROPE',
      India: 'IMEA',
      Italy: 'EUROPE',
      'Costa Rica': 'NAM',
      Hungary: 'EUROPE',
      Germany: 'EUROPE',
      Indonesia: 'EAJP',
      Taiwan: 'EAJP',
      Colombia: 'SAM',
      Morocco: 'IMEA',
      Peru: 'SAM',
      Finland: 'EUROPE',
      Serbia: 'EUROPE',
      Greece: 'EUROPE',
      Canada: 'NAM',
      Lebanon: 'IMEA',
      'Croatia (Hrvatska)': 'EUROPE',
      France: 'EUROPE',
      Japan: 'EAJP',
      Algeria: 'IMEA',
      'Sri Lanka': 'IMEA',
      Kenya: 'IMEA',
      Spain: 'EUROPE',
      'Korea (South)': 'EAJP',
      'Czech Republic': 'EUROPE',
      'United Arab Emirates': 'IMEA',
      'South Africa': 'IMEA',
      'Saudi Arabia': 'IMEA',
      Oman: 'IMEA',
      'Slovak Republic': 'EUROPE',
      China: 'CHINA',
      Australia: 'EAJP',
      Israel: 'EUROPE',
      Denmark: 'EUROPE',
      Poland: 'EUROPE',
      'Viet Nam': 'EAJP',
      'Hong Kong': 'CHINA',
      Kazakhstan: 'EUROPE',
      Portugal: 'EUROPE',
      'United States': 'NAM',
      Sweden: 'EUROPE',
      Malaysia: 'EAJP',
      Nigeria: 'IMEA',
      Egypt: 'IMEA',
      Pakistan: 'IMEA',
      Estonia: 'EUROPE'
    }
  }
  const processedData = pageCtl.report2Data.map(item => {
    const showLabel = countryLabel.includes(item.name)
    return {
      ...item,
      label: {
        show: showLabel,
        color: '#ffffff',
        fontsize: 12
      }
    }
  })

  let maxValue = Math.max.apply(null, pageCtl.report2Data.map(function (item) {
    return item.value
  }))
  if (isNaN(maxValue) || maxValue === -Infinity) {
    maxValue = 100
  }

  const title = 'End to End LT by Map [' + camelCaseStartPlaceholder(pageCtl.conditions.report2ResultType) + ']'
  return {
    title: {
      text: title
      // subtext: 'Data from DSS',
      // sublink:
      //     'https://scp-dss.cn.schneider-electric.com/#/customer/e2e_lt_analysis'
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        function formatValue (value) {
          if (value >= 1e9) {
            return (value / 1e9).toFixed(1) + 'B'
          } else if (value >= 1e6) {
            return (value / 1e6).toFixed(1) + 'M'
          } else if (value >= 1e3) {
            return (value / 1e3).toFixed(1) + 'K'
          } else {
            return value.toString()
          }
        }

        let unit = 'Days'
        if (pageCtl.conditions.report2ResultType === 'Net Value') {
          unit = '€'
        } else if (pageCtl.conditions.report2ResultType === 'Lines') {
          unit = 'Lines'
        } else if (pageCtl.conditions.report2ResultType === 'Number of Records') {
          unit = 'Records'
        } else if (pageCtl.conditions.report2ResultType === 'Quantity') {
          unit = 'Pcs'
        }

        const formattedValue = formatValue(params.value)

        let tip = `<div class="tooltip-main">
                 <div class="tooltip-item">
                   <span>${params.marker} ${params.name}</span>
                   <span style="float:right">&nbsp;${formattedValue} ${unit}</span>
                 </div>`

        if (params.data && params.data.tips) {
          const tooltips = params.data.tips
          tip += '<div style="border-top:1px dotted #ccc;margin-top:5px;padding-top:5px;">'
          for (const key in tooltips) {
            if (tooltips.hasOwnProperty(key)) {
              tip += `<div class="tooltip-item">
                    <span>${key.toUpperCase()}</span>
                    <span style="float:right">${tooltips[key]}</span>
                  </div>`
            }
          }
          tip += '</div>'
        }

        tip += '</div>'
        return tip
      }
    },
    toolbox: {
      show: true,
      orient: 'horizontal',
      left: 'right',
      top: 'top',
      feature: {
        dataView: { readOnly: false },
        restore: {},
        saveAsImage: {}
      }
    },

    visualMap: {
      min: pageCtl.conditions.report2VisualStart === '' ? 0 : pageCtl.conditions.report2VisualStart,
      max: pageCtl.conditions.report2VisualEnd === '' ? maxValue : pageCtl.conditions.report2VisualEnd, // 根据你的数据调整最大值
      text: ['High', 'Low'], // 图例标签
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#c13033',
          '#c43730',
          '#c8412d',
          '#cc4c27',
          '#d25924',
          '#d7671f',
          '#e1990a',
          '#e1a506',
          '#c6b601',
          '#87a40c',
          '#749d10',
          '#629512',
          '#539017',
          '#448a1a',
          '#36851a',
          '#2c821d'].reverse() // 渐变颜色
      },
      left: 20,
      bottom: 5, // 距离底部 20px
      orient: 'vertical', // 垂直方向显示（适合侧边）
      alignTo: 'right'
    },

    series: [{
      type: 'map',
      zoom: 1,
      left: 20,
      right: 20,
      top: 10,
      bottom: 10,
      roam: true, // 开启鼠标缩放和平移
      map: 'world e2e',
      data: processedData,
      nameMap
      // itemStyle: {
      //   areaColor: 'lightskyblue'
      // }
    }]
  }
})

const _report3SelectedCategory = computed(() => {
  if (pageCtl.conditions.report3Category.length > 0) {
    return pageCtl.conditions.report3Category
  } else {
    return ['COUNTRY_NAME']
  }
})
</script>
