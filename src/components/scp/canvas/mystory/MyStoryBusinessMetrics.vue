<template>
  <div class="my-story-header">
    <font-awesome-icon icon="chart-line"/>&nbsp;&nbsp;Business Metrics
  </div>

  <el-row class="my-story-business-metrics" :style="{height: _height + 'px'}" style="overflow: auto;margin-bottom: var(--scp-widget-margin)">
    <!-- Business Metrics Cards -->
    <el-col :span="24" class="subscript-container column-container">
      <scp-subscript id="MSBM1" ref="msbm1Ref"/>
      <div class="front">
        <div class="sub-title">Business Metrics</div>
        <div style="height: calc(100% - 1rem);overflow: auto">
          <el-row>
            <!-- Left Column: Business Metrics and KPI Metrics -->
            <el-col :span="24" style="padding-right: 0.125rem;">
              <!-- Business Metrics Card -->
              <div class="my-story-business-metrics-card">
                <el-card style="margin: 0 0.125rem 0.125rem 0.125rem">
                  <template #header>
                    <div class="card-header">
                      <span>Business Metrics</span>
                    </div>
                  </template>

                  <!-- Multi-level Headers -->
                  <el-row class="multi-level-header">
                    <el-col :span="6" class="sub-sub-title border-right-white">
                      Entity
                    </el-col>
                    <el-col :span="4" class="sub-sub-title border-right-white">
                      <div class="header-group">Order Intake</div>
                      <el-row class="sub-header">
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} MTD</el-col>
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} YoY</el-col>
                        <el-col :span="8" class="sub-header-item">WEIGHT</el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="4" class="sub-sub-title border-right-white">
                      <div class="header-group">Sales</div>
                      <el-row class="sub-header">
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} MTD</el-col>
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} YoY</el-col>
                        <el-col :span="8" class="sub-header-item">WEIGHT</el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="4" class="sub-sub-title border-right-white">
                      <div class="header-group">FCST</div>
                      <el-row class="sub-header">
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} MTD</el-col>
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} YoY</el-col>
                        <el-col :span="8" class="sub-header-item">WEIGHT</el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="6" class="sub-sub-title">
                      <div class="header-group">Backlog</div>
                      <el-row class="sub-header">
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} MTD</el-col>
                        <el-col :span="8" class="sub-header-item">{{ pageCtl.conditions.monthName }} YoY</el-col>
                        <el-col :span="8" class="sub-header-item">WEIGHT</el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <template #footer>
                    <el-table
                        :data="pageCtl.data.businessMetricsTree"
                        style="width: 100%; table-layout: fixed;"
                        row-key="id"
                        :show-header="false"
                        @expand-change="businessMetricsExpandChange"
                        border
                        lazy
                        :load="loadBusinessMetricsSubData"
                        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                        class="tree-table"
                        @row-dblclick="businessMetricsRowDblclick"
                        size="small">

                      <!-- Entity Column -->
                      <el-table-column prop="entity" label="Entity" width="200">
                        <template #default="scope">
                          <span :style="{color: 'var(--scp-text-color)', fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }">
                            {{ scope.row.entity }}
                          </span>
                        </template>
                      </el-table-column>

                      <!-- Order Intake Columns -->
                      <el-table-column label="Order Intake" width="150">
                        <el-table-column prop="orderIntakeMtd" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.orderIntakeMtdColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.orderIntakeMtd }}<sup v-if="scope.row.orderIntakeArrowValue !== undefined && scope.row.orderIntakeArrowValue !== null && scope.row.orderIntakeArrowValue !== 0"
                              class="arrow-percentage"
                              :style="{color: getArrowColor(scope.row.orderIntakeArrowColor)}">{{ formatArrowValue(scope.row.orderIntakeArrowValue) }}</sup>
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="orderIntakeYoy" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.orderIntakeYoyColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.orderIntakeYoy }}
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="orderIntakeWeight" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.orderIntakeWeightColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.orderIntakeWeight }}
                            </p>
                          </template>
                        </el-table-column>
                      </el-table-column>

                      <!-- Sales Columns -->
                      <el-table-column label="Sales" width="150">
                        <el-table-column prop="salesMtd" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.salesMtdColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.salesMtd }}<sup v-if="scope.row.salesArrowValue !== undefined && scope.row.salesArrowValue !== null && scope.row.salesArrowValue !== 0"
                              class="arrow-percentage"
                              :style="{color: getArrowColor(scope.row.salesArrowColor)}">{{ formatArrowValue(scope.row.salesArrowValue) }}</sup>
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="salesYoy" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.salesYoyColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.salesYoy }}
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="salesWeight" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.salesWeightColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.salesWeight }}
                            </p>
                          </template>
                        </el-table-column>
                      </el-table-column>

                      <!-- FCST Columns -->
                      <el-table-column label="FCST" width="150">
                        <el-table-column prop="fcstMtd" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.fcstMtdColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.fcstMtd }}<sup v-if="scope.row.fcstArrowValue !== undefined && scope.row.fcstArrowValue !== null && scope.row.fcstArrowValue !== 0"
                              class="arrow-percentage"
                              :style="{color: getArrowColor(scope.row.fcstArrowColor)}">{{ formatArrowValue(scope.row.fcstArrowValue) }}</sup>
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="fcstYoy" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.fcstYoyColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.fcstYoy }}
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="fcstWeight" width="50">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.fcstWeightColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.fcstWeight }}
                            </p>
                          </template>
                        </el-table-column>
                      </el-table-column>

                      <!-- Backlog Columns -->
                      <el-table-column label="Backlog" width="180">
                        <el-table-column prop="backlogMtd" width="60">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.backlogMtdColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.backlogMtd }}<sup v-if="scope.row.backlogArrowValue !== undefined && scope.row.backlogArrowValue !== null && scope.row.backlogArrowValue !== 0"
                              class="arrow-percentage"
                              :style="{color: getArrowColor(scope.row.backlogArrowColor)}">{{ formatArrowValue(scope.row.backlogArrowValue) }}</sup>
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="backlogYoy" width="60">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.backlogYoyColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center">
                              {{ scope.row.backlogYoy }}
                            </p>
                          </template>
                        </el-table-column>
                        <el-table-column prop="backlogWeight" width="60">
                          <template #default="scope">
                            <p :style="{color: convertValueColor(scope.row.backlogWeightColor), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                                  fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                               style="text-align: center; overflow: visible;">
                              {{ scope.row.backlogWeight }}
                            </p>
                          </template>
                        </el-table-column>
                      </el-table-column>
                    </el-table>
                  </template>
                </el-card>
              </div>

              <!-- KPI Metrics Card -->
              <my-story-kpi-merged-card
                  v-loading="pageCtl.loading.report1"
                  :month-name="pageCtl.conditions.monthName"
                  :otds-data="pageCtl.data.otds"
                  :otdm-data="pageCtl.data.otdm"
                  :clo-data="pageCtl.data.clo"
                  :otc-data="pageCtl.data.otc"/>
            </el-col>

            <!-- Right Column: Other Performance Cards -->
            <el-col :span="8" style="display: flex; flex-direction: column; padding-left: 0.125rem;">
              <my-story-business-metrics-card
                  v-loading="pageCtl.loading.report1"
                  card-type="PI"
                  title="Accuracy of Commit. in MyCP"
                  :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                  :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                  :sub-title4="'WEIGHT'"
                  :data="pageCtl.data.mycp"/>

              <my-story-business-metrics-card
                  v-loading="pageCtl.loading.report1"
                  card-type="PI"
                  title="SO NOR(OPM)"
                  :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                  :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                  :sub-title4="'WEIGHT'"
                  :data="pageCtl.data.sonor"/>

              <my-story-business-metrics-card
                  v-loading="pageCtl.loading.report1"
                  card-type="PI"
                  title="Effective PO NORO%"
                  :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                  :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                  :sub-title4="'WEIGHT'"
                  :data="pageCtl.data.ponoro"/>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="back">
        <div class="box" style="padding: 5px;width: 70%">
          <div class="box-title">Report Settings</div>
          <el-row>
            <el-col :span="4">Month</el-col>
            <el-col :span="16">
              <el-date-picker
                  style="width: var(--scp-input-width)"
                  v-model="pageCtl.conditions.month"
                  format="YYYYMM"
                  value-format="YYYYMM"
                  type="month"
                  :clearable="false"
                  placeholder="Month">
              </el-date-picker>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">Display</el-col>
            <el-col :span="16">
              <el-select v-model="pageCtl.conditions.display" placeholder="Display" size="small" style="width: var(--scp-input-width) !important;"
                         @click.stop="(e)=>e.preventDefault()"
                         collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="7" filterable
                  >
                  <el-option
                      v-for="item in ['ENTITY', 'CLUSTER_NAME', 'BU']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
            </el-col>
          </el-row>
          <el-divider/>
          <el-row>
            <el-col :span="8">Show <b>OTDS</b> as</el-col>
            <el-col :span="16">
              <el-radio-group v-model="pageCtl.conditions.otdsType">
                <el-radio value="ONTIME_PERCENTAGE">OnTime%</el-radio>
                <el-radio value="FAIL_LINES">Delay Lines%</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">Show <b>OTDM</b> as</el-col>
            <el-col :span="16">
              <el-radio-group v-model="pageCtl.conditions.otdmType">
                <el-radio value="ONTIME_PERCENTAGE">OnTime%</el-radio>
                <el-radio value="FAIL_LINES">Delay Lines%</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>

          <div class="box-footer">
            <el-button @click="msbm1Ref.toggleView()">Back</el-button>
            <el-button type="primary" @click="msbm1Ref.toggleView();search()">Search</el-button>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import MyStoryBusinessMetricsCard from '@/components/scp/canvas/mystory/MyStoryBusinessMetricsCard.vue'
import MyStoryBusinessMetricsMergedCard from '@/components/scp/canvas/mystory/MyStoryBusinessMetricsMergedCard.vue'
import MyStoryKpiMergedCard from '@/components/scp/canvas/mystory/MyStoryKpiMergedCard.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $deepClone: any = inject('$deepClone')
const $convertDateStr: any = inject('$convertDateStr')
const $firstCharUpperCase: any = inject('$firstCharUpperCase')

const msbm1Ref = ref()

const pageCtl = reactive({
  conditions: {
    $scpFilter: {},
    month: '',
    monthName: '',
    display: ['CLUSTER_NAME', 'ENTITY', 'BU'],
    otdsType: 'ONTIME_PERCENTAGE', // FAIL_LINES OR ONTIME_PERCENTAGE
    otdmType: 'ONTIME_PERCENTAGE' // FAIL_LINES OR ONTIME_PERCENTAGE
  },
  loading: {
    report1: false
  },
  data: {
    // Original performance result data
    otds: [],
    otdm: [],
    clo: [],
    otc: [],
    mycp: [],
    sonor: [],
    ponoro: [],
    din12: [],
    nydin: [],
    // New business metrics data
    orderIntake: [],
    sales: [],
    fcst: [],
    backlog: [],
    // Merged business metrics tree data
    businessMetricsTree: []
  }
})

const renderValue = (value, unit?) => {
  if (isNaN(value)) {
    return '--'
  } if (value === 0) {
    return '0'
  } else {
    return value + (unit || '')
  }
}

const initPage = () => {
  const start = new Date()
  let startMonth: any = start.getMonth() + 1
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  if (!pageCtl.conditions.month) {
    pageCtl.conditions.month = start.getFullYear() + '' + startMonth
  }
  search()
}

const search = () => {
  pageCtl.conditions.monthName = $firstCharUpperCase($convertDateStr(pageCtl.conditions.month).split('-')[0])
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_business_metrics/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    // Process OTDS data
    if (pageCtl.conditions.otdsType === 'FAIL_LINES') {
      pageCtl.data.otds = body.otds.map(e => {
        e.value2Color = 'none'
        e.value3Color = 'none'
        e.value2 = renderValue(e.value2, '')
        e.value3 = renderValue(e.value3, '')

        e.children.map(e2 => {
          e2.value2Color = 'none'
          e2.value3Color = 'none'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    } else {
      pageCtl.data.otds = body.otds.map(e => {
        e.value2Color = e.value2 >= 97.5 ? 'green' : 'red'
        e.value3Color = e.value3 >= 97.5 ? 'green' : 'red'
        e.value2 = renderValue(e.value2, '%')
        e.value3 = renderValue(e.value3, '%')

        e.children.map(e2 => {
          e2.value2Color = e2.value2 >= 97.5 ? 'green' : 'red'
          e2.value3Color = e2.value3 >= 97.5 ? 'green' : 'red'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    }

    // Process OTDM data
    if (pageCtl.conditions.otdmType === 'FAIL_LINES') {
      pageCtl.data.otdm = body.otdm.map(e => {
        e.value2Color = 'none'
        e.value3Color = 'none'
        e.value2 = renderValue(e.value2, '')
        e.value3 = renderValue(e.value3, '')

        e.children.map(e2 => {
          e2.value2Color = 'none'
          e2.value3Color = 'none'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    } else {
      pageCtl.data.otdm = body.otdm.map(e => {
        e.value2Color = e.value2 >= 90 ? 'green' : 'red'
        e.value3Color = e.value3 >= 90 ? 'green' : 'red'
        e.value2 = renderValue(e.value2, '%')
        e.value3 = renderValue(e.value3, '%')

        e.children.map(e2 => {
          e2.value2Color = e2.value2 >= 90 ? 'green' : 'red'
          e2.value3Color = e2.value3 >= 90 ? 'green' : 'red'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    }

    // Process CLO data
    pageCtl.data.clo = body.clo.map(e => {
      e.value2Color = e.value2 >= 95 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 95 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        return e2
      })
      return e
    })

    // Process MyCP data
    pageCtl.data.mycp = body.mycp.map(e => {
      e.value2Color = e.value2 >= 95 ? 'green' : 'red'
      e.value3Color = e.value3 >= 95 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 95 ? 'green' : 'red'
        e2.value3Color = e2.value3 >= 95 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    // Process PO NORO data
    pageCtl.data.ponoro = body.ponoro.map(e => {
      e.value2Color = e.value2 < 5 ? 'green' : 'red'
      e.value3Color = e.value3 < 5 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 < 5 ? 'green' : 'red'
        e2.value3Color = e2.value3 < 5 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    // Process SO NOR data
    pageCtl.data.sonor = body.sonor.map(e => {
      e.value2Color = e.value2 < 2 ? 'green' : 'red'
      e.value3Color = e.value3 < 2 ? 'green' : 'red'
      e.value2 = renderValue(e.value2)
      e.value3 = renderValue(e.value3)

      e.children.map(e2 => {
        e2.value2Color = e2.value2 < 2 ? 'green' : 'red'
        e2.value3Color = e2.value3 < 2 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2)
        e2.value3 = renderValue(e2.value3)
        return e2
      })
      return e
    })

    // Process OTC data
    pageCtl.data.otc = body.otc.map(e => {
      e.value2Color = e.value2 >= 97.7 ? 'green' : 'red'
      e.value3Color = e.value3 >= 97.7 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 97.7 ? 'green' : 'red'
        e2.value3Color = e2.value3 >= 97.7 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    // Process orderIntake data
    pageCtl.data.orderIntake = body.orderIntake.map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')
      // Keep arrowValue as number for formatArrowValue function
      // e.arrowValue = renderValue(e.arrowValue, '%')

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        // Keep arrowValue as number for formatArrowValue function
        // e2.arrowValue = renderValue(e2.arrowValue, '%')
        return e2
      })
      return e
    })

    // Process business metrics data
    pageCtl.data.sales = (body.sales || []).map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')
      // Keep arrowValue as number for formatArrowValue function

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        return e2
      })
      return e
    })

    pageCtl.data.fcst = (body.fcst || []).map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        return e2
      })
      return e
    })

    pageCtl.data.backlog = (body.backlog || []).map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')
      // Keep arrowValue as number for formatArrowValue function

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        return e2
      })
      return e
    })

    // Process business metrics tree data - merge all data sources
    pageCtl.data.businessMetricsTree = mergeAndParseBusinessMetricsData(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

// 辅助函数：根据display字段构建entity显示值（参照Tracking.vue的buildCategoryValue）
const buildEntityValue = (rawData) => {
  if (!Array.isArray(pageCtl.conditions.display) || pageCtl.conditions.display.length === 0) {
    return rawData.value1 || rawData.entity || ''
  }

  // 根据选择的字段数量，组合对应的entity值
  const entityParts = []
  for (let i = 0; i < pageCtl.conditions.display.length; i++) {
    const displayField = pageCtl.conditions.display[i]
    // 将display字段映射到数据字段，类似category1, category2, category3
    let fieldValue = ''
    switch (displayField) {
      case 'ENTITY':
        fieldValue = rawData.entity || rawData.value1 || ''
        break
      case 'CLUSTER_NAME':
        fieldValue = rawData.clusterName || rawData.cluster_name || ''
        break
      case 'BU':
        fieldValue = rawData.bu || rawData.BU || ''
        break
      default:
        // 尝试直接使用字段名
        fieldValue = rawData[displayField] || rawData[displayField.toLowerCase()] || ''
    }
    if (fieldValue) {
      entityParts.push(fieldValue)
    }
  }

  return entityParts.length > 0 ? entityParts.join(' - ') : (rawData.value1 || rawData.entity || '')
}

// 合并并处理业务指标数据
const mergeAndParseBusinessMetricsData = (body) => {
  const entityMap = new Map()

  // 处理各种业务指标数据
  const processDataSource = (data, prefix) => {
    if (!data || !Array.isArray(data)) return

    data.forEach(item => {
      const entityKey = buildEntityValue(item)

      if (!entityMap.has(entityKey)) {
        entityMap.set(entityKey, {
          id: item.id || `${entityKey}_${Date.now()}`,
          entity: entityKey,
          rowType: item.rowType || 'root',
          hasChildren: item.hasChildren,
          parentName: item.parentName || [],
          level: (item.parentName || []).length
        })
      }

      const mergedItem = entityMap.get(entityKey)

      // 根据数据源设置对应的字段
      if (prefix === 'orderIntake') {
        mergedItem.orderIntakeMtd = item.value2 || '--'
        mergedItem.orderIntakeMtdColor = item.value2Color || item.arrowColor
        mergedItem.orderIntakeYoy = item.value3 || '--'
        mergedItem.orderIntakeYoyColor = item.value3Color
        mergedItem.orderIntakeWeight = item.value4 || '--'
        mergedItem.orderIntakeWeightColor = item.value4Color
        mergedItem.orderIntakeArrowValue = item.arrowValue
        mergedItem.orderIntakeArrowColor = item.arrowColor
      } else if (prefix === 'sales') {
        mergedItem.salesMtd = item.value2 || '--'
        mergedItem.salesMtdColor = item.value2Color || item.arrowColor
        mergedItem.salesYoy = item.value3 || '--'
        mergedItem.salesYoyColor = item.value3Color
        mergedItem.salesWeight = item.value4 || '--'
        mergedItem.salesWeightColor = item.value4Color
        mergedItem.salesArrowValue = item.arrowValue
        mergedItem.salesArrowColor = item.arrowColor
      } else if (prefix === 'fcst') {
        mergedItem.fcstMtd = item.value2 || '--'
        mergedItem.fcstMtdColor = item.value2Color || item.arrowColor
        mergedItem.fcstYoy = item.value3 || '--'
        mergedItem.fcstYoyColor = item.value3Color
        mergedItem.fcstWeight = item.value4 || '--'
        mergedItem.fcstWeightColor = item.value4Color
        mergedItem.fcstArrowValue = item.arrowValue
        mergedItem.fcstArrowColor = item.arrowColor
      } else if (prefix === 'backlog') {
        mergedItem.backlogMtd = item.value2 || '--'
        mergedItem.backlogMtdColor = item.value2Color || item.arrowColor
        mergedItem.backlogYoy = item.value3 || '--'
        mergedItem.backlogYoyColor = item.value3Color
        mergedItem.backlogWeight = item.value4 || '--'
        mergedItem.backlogWeightColor = item.value4Color
        mergedItem.backlogArrowValue = item.arrowValue
        mergedItem.backlogArrowColor = item.arrowColor
      }
    })
  }

  // 处理各个数据源
  processDataSource(body.orderIntake, 'orderIntake')
  processDataSource(body.sales, 'sales')
  processDataSource(body.fcst, 'fcst')
  processDataSource(body.backlog, 'backlog')

  // 为没有数据的字段设置默认值
  const result = Array.from(entityMap.values()).map(item => ({
    ...item,
    // Order Intake defaults
    orderIntakeMtd: item.orderIntakeMtd || '--',
    orderIntakeMtdColor: item.orderIntakeMtdColor || 'var(--scp-text-color)',
    orderIntakeYoy: item.orderIntakeYoy || '--',
    orderIntakeYoyColor: item.orderIntakeYoyColor || 'var(--scp-text-color)',
    orderIntakeWeight: item.orderIntakeWeight || '--',
    orderIntakeWeightColor: item.orderIntakeWeightColor || 'var(--scp-text-color)',
    orderIntakeArrowValue: item.orderIntakeArrowValue,
    orderIntakeArrowColor: item.orderIntakeArrowColor || 'var(--scp-text-color)',

    // Sales defaults
    salesMtd: item.salesMtd || '--',
    salesMtdColor: item.salesMtdColor || 'var(--scp-text-color)',
    salesYoy: item.salesYoy || '--',
    salesYoyColor: item.salesYoyColor || 'var(--scp-text-color)',
    salesWeight: item.salesWeight || '--',
    salesWeightColor: item.salesWeightColor || 'var(--scp-text-color)',
    salesArrowValue: item.salesArrowValue,
    salesArrowColor: item.salesArrowColor || 'var(--scp-text-color)',

    // FCST defaults
    fcstMtd: item.fcstMtd || '--',
    fcstMtdColor: item.fcstMtdColor || 'var(--scp-text-color)',
    fcstYoy: item.fcstYoy || '--',
    fcstYoyColor: item.fcstYoyColor || 'var(--scp-text-color)',
    fcstWeight: item.fcstWeight || '--',
    fcstWeightColor: item.fcstWeightColor || 'var(--scp-text-color)',
    fcstArrowValue: item.fcstArrowValue,
    fcstArrowColor: item.fcstArrowColor || 'var(--scp-text-color)',

    // Backlog defaults
    backlogMtd: item.backlogMtd || '--',
    backlogMtdColor: item.backlogMtdColor || 'var(--scp-text-color)',
    backlogYoy: item.backlogYoy || '--',
    backlogYoyColor: item.backlogYoyColor || 'var(--scp-text-color)',
    backlogWeight: item.backlogWeight || '--',
    backlogWeightColor: item.backlogWeightColor || 'var(--scp-text-color)',
    backlogArrowValue: item.backlogArrowValue,
    backlogArrowColor: item.backlogArrowColor || 'var(--scp-text-color)'
  }))

  return result
}

// 子数据加载函数（参照Tracking.vue的load函数）
const loadBusinessMetricsSubData = (tree, treeNode, resolve) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_business_metrics/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parentName: tree.parentName || [],
      expandValue: tree.entity
    }
  }).then((body) => {
    resolve(mergeAndParseBusinessMetricsData(body))
  }).catch((error) => {
    console.log(error)
  })
}

// 展开变化事件处理（参照Tracking.vue的report1ExpandChange）
const businessMetricsExpandChange = (row) => {
  if (pageCtl.report1LastExpandRow && row.id === pageCtl.report1LastExpandRow.id) {
    return
  }
  pageCtl.report1LastExpandRow = row
}

// 双击行事件处理
const businessMetricsRowDblclick = (row) => {
  console.log('Double clicked row:', row)
}

// 辅助函数：转换颜色值
const convertValueColor = (color) => {
  if (!color) return 'var(--scp-text-color)'
  return color
}

// 辅助函数：获取箭头颜色
const getArrowColor = (color) => {
  if (!color) return 'var(--scp-text-color)'
  return color
}

// 辅助函数：格式化箭头值
const formatArrowValue = (value) => {
  if (value === undefined || value === null || value === 0) return ''
  return value > 0 ? `↑${value}%` : `↓${Math.abs(value)}%`
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'BUSINESS_METRICS'
const $store = useStore()
const commentRef: any = ref()

const _height = computed(() => {
  return Math.max($store.state.pageHeight - 272, 200)
})

const resize = () => {
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryBusinessMetrics'
}
</script>

<style lang="scss">
.my-story-business-metrics {
  margin-top: calc(var(--scp-widget-margin) + var(--scp-widget-margin));

  .column-container {
    display: flex;
    justify-items: center;
    flex-direction: column;
    padding-bottom: calc(var(--scp-widget-margin) + var(--scp-widget-margin));
    height: 100%;
    margin-bottom: 0 !important;

    .sub-title {
      text-align: center;
      font-size: 0.55rem;
      padding: 0;
      font-weight: bold;
      margin-bottom: var(--scp-widget-margin);
      color: var(--scp-text-color-secondary);
    }
  }

  .kpi-tips {
    text-align: center;
    color: #fff;
    font-weight: bold;
  }

  // 多级表头样式
  .my-story-business-metrics-card {
    .multi-level-header {
      background-color: var(--scp-card-header-bg);
      border: 1px solid var(--scp-border-color);
      border-bottom: none;

      .sub-sub-title {
        text-align: center;
        font-size: 0.55rem;
        font-weight: bold;
        color: var(--scp-text-color);
        padding: 8px 4px;
        border-right: 1px solid var(--scp-border-color);

        &.border-right-white {
          border-right: 1px solid var(--scp-border-color);
        }

        .header-group {
          padding: 4px 0;
          border-bottom: 1px solid var(--scp-border-color);
          margin-bottom: 4px;
        }

        .sub-header {
          .sub-header-item {
            font-size: 0.45rem;
            font-weight: normal;
            padding: 2px;
            text-align: center;
            border-right: 1px solid var(--scp-border-color-light);

            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }

    .tree-table {
      .arrow-percentage {
        font-size: 0.4rem;
        margin-left: 2px;
      }
    }
  }
}
</style>
