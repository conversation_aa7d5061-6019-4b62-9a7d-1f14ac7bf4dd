<template>
  <div class="my-story-business-metrics-merged-card">
    <el-card style="margin: 0 0.125rem 0.125rem 0.125rem">
      <template #header>
        <div class="card-header">
          <span>Business Metrics</span>
        </div>
      </template>

      <!-- Multi-level Headers -->
      <div class="multi-level-headers">
        <!-- First Level Headers -->
        <div class="header-row first-level-header">
          <div class="header-cell entity-header border-right-white">
            Entity
          </div>
          <div class="header-cell group-header border-right-white">
            Order Intake
          </div>
          <div class="header-cell group-header border-right-white">
            Sales
          </div>
          <div class="header-cell group-header border-right-white">
            FCST
          </div>
          <div class="header-cell group-header">
            Backlog
          </div>
        </div>

        <!-- Second Level Headers -->
        <div class="header-row second-level-header">
          <div class="header-cell entity-header border-right-white">
            <!-- Empty for Entity column -->
          </div>
          <div class="sub-headers-group border-right-white">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
          <div class="sub-headers-group border-right-white">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
          <div class="sub-headers-group border-right-white">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
          <div class="sub-headers-group">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-table
            :data="mergedData"
            style="width: 100%; table-layout: fixed;"
            row-key="id"
            :show-header="false"
            @expand-change="mergedCardExpandChange"
            border
            lazy
            :load="load"
            :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
            class="tree-table"
            @row-dblclick="report1RowDblick"
            @sort-change="report1SortChange"
            @contextmenu.prevent=""
            size="small">

        <!-- Entity Column -->
        <el-table-column prop="entity" label="Entity" width="100" fixed="left">
          <template #default="scope">
            <div class="entity-content" :style="{ paddingLeft: (scope.row.level || 0) * 18 + 'px' }">
              <span :style="{
                color: 'var(--scp-text-color-primary)',
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }">
                {{ scope.row.entity }}
              </span>
            </div>
          </template>
        </el-table-column>

        <!-- Order Intake Columns -->
        <el-table-column label="Order Intake MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.orderIntake?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.orderIntake?.mtd || '--' }}
              <sup v-if="scope.row.orderIntake?.arrowValue !== undefined && scope.row.orderIntake?.arrowValue !== null && scope.row.orderIntake?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.orderIntake?.arrowColor)}">
                {{ formatArrowValue(scope.row.orderIntake?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Order Intake Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.orderIntake?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.orderIntake?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Order Intake Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.orderIntake?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.orderIntake?.weight || '--' }}
            </p>
          </template>
        </el-table-column>

        <!-- Sales Columns -->
        <el-table-column label="Sales MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.sales?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.sales?.mtd || '--' }}
              <sup v-if="scope.row.sales?.arrowValue !== undefined && scope.row.sales?.arrowValue !== null && scope.row.sales?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.sales?.arrowColor)}">
                {{ formatArrowValue(scope.row.sales?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Sales Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.sales?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.sales?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Sales Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.sales?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.sales?.weight || '--' }}
            </p>
          </template>
        </el-table-column>

        <!-- FCST Columns -->
        <el-table-column label="FCST MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.fcst?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.fcst?.mtd || '--' }}
              <sup v-if="scope.row.fcst?.arrowValue !== undefined && scope.row.fcst?.arrowValue !== null && scope.row.fcst?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.fcst?.arrowColor)}">
                {{ formatArrowValue(scope.row.fcst?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="FCST Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.fcst?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.fcst?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="FCST Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.fcst?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.fcst?.weight || '--' }}
            </p>
          </template>
        </el-table-column>

        <!-- Backlog Columns -->
        <el-table-column label="Backlog MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.backlog?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.backlog?.mtd || '--' }}
              <sup v-if="scope.row.backlog?.arrowValue !== undefined && scope.row.backlog?.arrowValue !== null && scope.row.backlog?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.backlog?.arrowColor)}">
                {{ formatArrowValue(scope.row.backlog?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Backlog Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.backlog?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.backlog?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Backlog Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.backlog?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.backlog?.weight || '--' }}
            </p>
          </template>
        </el-table-column>
        </el-table>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, ref } from 'vue'

const $axios: any = inject('$axios')

interface CardData {
  rowType: string;
  value1: string;
  value1Color?: string;
  value2: string | number;
  value2Color?: string;
  value3: string | number;
  value3Color?: string;
  value4?: string | number;
  value4Color?: string;
  arrow?: string;
  arrowColor?: string;
  arrowValue?: number;
  children?: CardData[];
}

interface MergedCardData {
  id: string;
  entity: string;
  rowType: string;
  hasChildren?: boolean;
  parentName?: string[];
  level?: number;
  orderIntake?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  sales?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  fcst?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  backlog?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  children?: MergedCardData[];
}

const props = withDefaults(defineProps<{
  monthName: string;
  treeData?: Array<MergedCardData>;
  conditions?: any;
}>(), {
  monthName: '',
  treeData: () => [],
  conditions: () => ({})
})

// 子数据加载函数（参照Tracking.vue的load函数）
const load = (tree: any, treeNode: any, resolve: (data: any[]) => void) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_business_metrics/query_report1_sub',
    data: {
      ...props.conditions,
      parentName: tree.parentName || [],
      expandValue: tree.entity
    }
  }).then((body) => {
    resolve(parseSubData(body))
  }).catch((error) => {
    console.log(error)
  })
}

// 辅助函数：根据display字段构建entity显示值（参照Tracking.vue的buildCategoryValue）
const buildEntityValue = (rawData) => {
  if (!Array.isArray(props.conditions.display) || props.conditions.display.length === 0) {
    return rawData.value1 || rawData.entity || ''
  }

  // 根据选择的字段数量，组合对应的entity值
  const entityParts = []
  for (let i = 0; i < props.conditions.display.length; i++) {
    const displayField = props.conditions.display[i]
    // 将display字段映射到数据字段，类似category1, category2, category3
    let fieldValue = ''
    switch (displayField) {
      case 'ENTITY':
        fieldValue = rawData.entity || rawData.value1 || ''
        break
      case 'CLUSTER_NAME':
        fieldValue = rawData.clusterName || rawData.cluster_name || ''
        break
      case 'BU':
        fieldValue = rawData.bu || rawData.BU || ''
        break
      default:
        // 尝试直接使用字段名
        fieldValue = rawData[displayField] || rawData[displayField.toLowerCase()] || ''
    }
    if (fieldValue) {
      entityParts.push(fieldValue)
    }
  }

  return entityParts.length > 0 ? entityParts.join(' - ') : (rawData.value1 || rawData.entity || '')
}

// 解析子数据的方法（参照Tracking.vue的parseReport1Data简化版本）
const parseSubData = (body: any) => {
  // 如果返回的是分类数据，需要合并处理
  if (body && body.orderIntake && Array.isArray(body.orderIntake)) {
    return parseSubDataFromCategories(body)
  }

  // 如果是直接的数组数据
  let dataArray = body
  if (body && body.data && Array.isArray(body.data)) {
    dataArray = body.data
  }

  if (!Array.isArray(dataArray)) {
    return []
  }

  return dataArray.map((item: any) => ({
    id: item.id || `${buildEntityValue(item)}_${Date.now()}`,
    entity: buildEntityValue(item),
    rowType: item.rowType || 'child',
    hasChildren: item.hasChildren, // 直接使用后端返回的值，不做复杂判断
    parentName: item.parentName || [],
    level: (item.parentName || []).length + 1,
    orderIntake: {
      mtd: item.value2 || '--',
      mtdColor: item.value2Color || item.arrowColor,
      yoy: item.value3 || '--',
      yoyColor: item.value3Color,
      weight: item.value4 || '--',
      weightColor: item.value4Color,
      arrowValue: item.arrowValue,
      arrowColor: item.arrowColor
    },
    sales: {
      mtd: '--',
      mtdColor: 'var(--scp-text-color)',
      yoy: '--',
      yoyColor: 'var(--scp-text-color)',
      weight: '--',
      weightColor: 'var(--scp-text-color)',
      arrowValue: null,
      arrowColor: 'var(--scp-text-color)'
    },
    fcst: {
      mtd: '--',
      mtdColor: 'var(--scp-text-color)',
      yoy: '--',
      yoyColor: 'var(--scp-text-color)',
      weight: '--',
      weightColor: 'var(--scp-text-color)',
      arrowValue: null,
      arrowColor: 'var(--scp-text-color)'
    },
    backlog: {
      mtd: '--',
      mtdColor: 'var(--scp-text-color)',
      yoy: '--',
      yoyColor: 'var(--scp-text-color)',
      weight: '--',
      weightColor: 'var(--scp-text-color)',
      arrowValue: null,
      arrowColor: 'var(--scp-text-color)'
    }
  }))
}

// 处理分类数据格式的子数据
const parseSubDataFromCategories = (body: any) => {
  const entityMap = new Map()

  // 处理四种业务指标数据
  const processData = (data: any[], type: string) => {
    if (!data || !Array.isArray(data)) return

    data.forEach(item => {
      // 使用动态构建的entity值作为key
      const entityKey = buildEntityValue(item)
      if (!entityMap.has(entityKey)) {
        // 简化hasChildren逻辑：直接使用后端返回的值
        const hasChildren = item.hasChildren

        entityMap.set(entityKey, {
          id: entityKey,
          entity: entityKey,
          rowType: item.rowType || 'child',
          hasChildren,
          parentName: item.parentName || [],
          level: (item.parentName || []).length + 1
        })
      }

      const mergedItem = entityMap.get(entityKey)

      const cardData = {
        mtd: item.value2 || '--',
        mtdColor: item.value2Color || item.arrowColor,
        yoy: item.value3 || '--',
        yoyColor: item.value3Color,
        weight: item.value4 || '--',
        weightColor: item.value4Color,
        arrowValue: item.arrowValue,
        arrowColor: item.arrowColor
      }

      mergedItem[type] = cardData
    })
  }

  // 处理各种业务指标
  processData(body.orderIntake || [], 'orderIntake')
  processData(body.sales || [], 'sales')
  processData(body.fcst || [], 'fcst')
  processData(body.backlog || [], 'backlog')
  console.log('parseSubDataFromCategories result:', Array.from(entityMap.values()))

  return Array.from(entityMap.values())
}

// 直接使用传入的树形数据
const mergedData = computed(() => {
  console.log('Raw treeData:', props.treeData)
  return props.treeData || []
})

// 展开变化事件处理
const mergedCardExpandChange = (row: MergedCardData) => {
  console.log('Expanded row:', row)
}

// 双击行事件处理
const report1RowDblick = (row: MergedCardData) => {
  console.log('Double clicked row:', row)
}

// 排序变化事件处理
const report1SortChange = (e: any) => {
  console.log('Sort change:', e)
}

const getArrowColor = (arrowColor) => {
  if (arrowColor === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (arrowColor === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-warning)'
  }
}

const formatArrowValue = (value) => {
  if (value === undefined || value === null || value === '' || value === 0) {
    return ''
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) {
    return ''
  }

  const sign = numValue >= 0 ? '+' : ''
  let displayValue = numValue
  if (Math.abs(numValue) >= 1000) {
    displayValue = (numValue / 1000).toFixed(1) + 'K'
  } else {
    displayValue = numValue.toFixed(1)
  }

  return `${sign}${displayValue}%`
}

const convertValueColor = (colorName) => {
  if (colorName === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (colorName === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-primary)'
  }
}
</script>

<script lang="ts">
export default {
  name: 'MyStoryBusinessMetricsMergedCard'
}
</script>

<style lang="scss">
.my-story-business-metrics-merged-card {
  .el-card__header {
    padding: 0.25rem !important;
    text-align: center;
    font-size: 0.55rem !important;
    font-weight: bold;
    color: #fff;
    background-color: var(--scp-bg-color-highlight) !important;
  }

  .el-card__body {
    color: #fff;
    padding: 0.15rem 0 !important;
    background-color: var(--scp-bg-color-highlight) !important;
  }

  .multi-level-headers {
    .header-row {
      display: flex;
      width: 100%;

      &.first-level-header {
        .header-cell {
          text-align: center;
          font-size: 0.5rem;
          font-weight: bold;
          padding: 0.25rem 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .entity-header {
          width: 100px;
          flex-shrink: 0;
        }

        .group-header {
          flex: 1;
          min-width: 280px;
        }
      }

      &.second-level-header {
        .entity-header {
          width: 100px;
          flex-shrink: 0;
        }

        .sub-headers-group {
          flex: 1;
          display: flex;
          min-width: 280px;

          .sub-header-cell {
            flex: 1;
            text-align: center;
            font-size: 0.45rem;
            font-weight: normal;
            padding: 0.2rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .border-right-white {
      border-right: 1px solid #fff;
    }

    .border-right-thin {
      border-right: 1px solid rgba(255, 255, 255, 0.3);
    }
  }

  .el-card__footer {
    padding: var(--scp-widget-margin);

    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: 0 !important;
    }

    .el-table__empty-block {
      min-height: auto !important;
    }

    .el-table__empty-text {
      line-height: 0.94rem !important;
    }

    .el-table--small .el-table__cell {
      padding: 0 !important;
      overflow: visible !important;
    }

    .el-table .cell {
      line-height: 21px !important;
      overflow: visible !important;
    }

    // 确保树形表格的缩进正常显示，支持多层级缩进
    .tree-table {
      td:first-child {
        .cell {
          display: flex;
          align-items: center;

          .el-table__indent {
            display: inline-block !important;
            width: 18px;
            height: 1px;
            flex-shrink: 0; // 防止缩进被压缩
          }

          // 支持多层级缩进，每个层级增加18px缩进
          .el-table__indent[style*="padding-left"] {
            display: inline-block !important;
          }

          // 自定义缩进容器样式
          .entity-content {
            display: flex;
            align-items: center;
            width: 100%;

            span {
              flex: 1;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  .arrow-percentage {
    font-size: 0.35rem !important;
    font-weight: normal !important;
    line-height: 1;
    margin-left: 2px;
    vertical-align: super;
  }
}
</style>
